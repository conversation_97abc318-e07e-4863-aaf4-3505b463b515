// Custom plugin wrapper for expo-linking to avoid TypeScript issues
// This plugin ensures expo-linking native module is properly registered

const { withPlugins, withInfoPlist, withAndroidManifest } = require('@expo/config-plugins');

function withExpoLinking(config) {
  // Ensure expo-linking is properly configured for both iOS and Android

  // For iOS: Add URL scheme handling if not already present
  config = withInfoPlist(config, (config) => {
    const infoPlist = config.modResults;

    // Ensure URL schemes are properly configured
    if (!infoPlist.CFBundleURLTypes) {
      infoPlist.CFBundleURLTypes = [];
    }

    // Check if our scheme is already configured
    const existingScheme = infoPlist.CFBundleURLTypes.find(
      (urlType) => urlType.CFBundleURLSchemes && urlType.CFBundleURLSchemes.includes(config.scheme)
    );

    if (!existingScheme && config.scheme) {
      infoPlist.CFBundleURLTypes.push({
        CFBundleURLName: config.slug,
        CFBundleURLSchemes: [config.scheme],
      });
    }

    return config;
  });

  // For Android: Ensure intent filters are properly configured
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;

    // The expo-linking module should handle Android configuration automatically
    // via its expo-module.config.json, but we ensure the manifest is ready

    return config;
  });

  return config;
}

module.exports = withExpoLinking;
